package com.cec.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.ChangeDelay;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.bo.ChangeCancelBo;
import com.cec.business.domain.bo.ChangeInfoBo;
import com.cec.business.domain.bo.ChangeItemBo;
import com.cec.business.domain.bo.ChangeRecordBo2;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.ChangeDelayVo;
import com.cec.business.domain.vo.ChangeInfoVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.extra.req.FlowTaskHandlerReqVO;
import com.cec.business.extra.resp.FlowInstanceStartResp;
import com.cec.business.extra.resp.FlowTaskCurrentResp;
import com.cec.business.extra.resp.FlowTaskNextListResp;
import com.cec.business.extra.resp.FlowTaskStrategyResp;
import com.cec.business.mapper.ChangeDelayMapper;
import com.cec.business.mapper.ChangeInfoMapper;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.mapper.ChangeRecordMapper;
import com.cec.business.service.EmailService;
import com.cec.business.service.IChangeInfoService;
import com.cec.business.service.IModifyLogService;
import com.cec.business.service.SmartFlowService;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.DateUtils;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.system.domain.vo.SysUserVo;
import org.apache.commons.lang3.StringUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.redis.utils.SequenceUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.vo.UserVo;
import com.cec.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 变更申请Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ChangeInfoServiceImpl implements IChangeInfoService {

    private final ChangeInfoMapper changeInfoMapper;
    private final ChangeItemMapper changeItemMapper;
    private final ChangeRecordMapper changeRecordMapper;
    private final IModifyLogService modifyLogService;
    private final SmartFlowService smartFlowService;
    private final SysUserMapper userMapper;
    private final EmailService emailService;
    private final ChangeDelayMapper changeDelayMapper;
    private final CecSmartFlowConfig cecSmartFlowConfig;

    private static final String TEAM_MANAGER_KEY = "teamManager";
    private static final String IMPLEMENTER_KEY = "implementer";
    private static final String REVIEWER_KEY = "reviewer";
    private static final String CHANGE_TYPE_KEY = "changeType";
    private static final String DEPT_HEAD_KEY = "deptHead";
    private static final String TEAM_LEADER_KEY = "teamLeader";
    private static final String DSI_FIRST_REVIEWER_KEY = "dsiFirstReviewer";
    private static final String EMERGENCY_REVIEWER_KEY = "emergencyReviewer";

    // 节点名称常量
    private static final String NODE_TEAM_MANAGER_APPROVAL = "0-team manager审批";
    private static final String NODE_TEAM_LEADER_APPROVAL = "0-team leader审批";
    private static final String NODE_DEPT_LEADER_APPROVAL = "0-部门领导审批";
    private static final String NODE_VIRTUAL_GROUP_APPROVAL = "0-虚拟小组审批";
    private static final String NODE_IMPLEMENTER = "1-实施者";
    private static final String NODE_IMPLEMENTING = "1-实施中";
    private static final String NODE_REVIEWER = "2-reviewer";
    private static final String NODE_EMERGENCY_REVIEWER = "2-紧急Reviewer";

    // 邮件发送重试参数
    private static final int EMAIL_MAX_RETRIES = 3;
    private static final int EMAIL_RETRY_DELAY_MS = 2000; // 2秒

    /**
     * 查询变更申请
     *
     * @param id 主键
     * @return 变更申请
     */
    @Override
    public ChangeInfoVo queryById(Long id) {
        ChangeInfoVo infoVo = changeInfoMapper.selectVoById(id);
        if (ObjectUtil.isNull(infoVo)) {
            return null;
        }
        ChangeItemVo itemVo = changeItemMapper.selectVoOne(new LambdaQueryWrapper<ChangeItem>().eq(ChangeItem::getChangeId, infoVo.getId()));
        Optional.ofNullable(itemVo).ifPresent(x -> {
            infoVo.setChangeCode(x.getChangeCode());
            infoVo.setStage(x.getStage());
        });
        return infoVo;
    }

    @Override
    public TableDataInfo<ChangeInfoVo> queryPageList(ChangeInfoBo bo, PageQuery pageQuery) {
        return null;
    }

    /**
     * 分页查询变更申请列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请分页列表
     */
    @Override
    public TableDataInfo<ChangeItemVo> queryPageList(ChangeItemBo bo, PageQuery pageQuery) {
        // 记录传入的字段列表，便于调试
        logSelectedFields(bo.getSelectFiledNameList());

        // 确定要返回的字段
        List<String> selectedFields = determineSelectedFields(bo.getSelectFiledNameList());

        // 查询数据
        Page<ChangeItemVo> result = queryChangeItems(bo, pageQuery);
        List<ChangeItemVo> records = result.getRecords();

        if (CollUtil.isNotEmpty(records)) {
            // 处理字段过滤和额外数据填充
            List<ChangeItemVo> filteredRecords = processRecords(records, selectedFields);
            result.setRecords(filteredRecords);
        }

        return TableDataInfo.build(result);
    }

    /**
     * 记录选中的字段列表用于调试
     */
    private void logSelectedFields(List<String> selectFiledNameList) {
        if (CollUtil.isNotEmpty(selectFiledNameList)) {
            log.info("查询指定字段列表: {}", String.join(",", selectFiledNameList));
        }
    }

    /**
     * 确定要返回的字段列表
     */
    private List<String> determineSelectedFields(List<String> selectFiledNameList) {
        return CollUtil.isNotEmpty(selectFiledNameList) ?
            selectFiledNameList :
            getDefaultFields();
    }

    /**
     * 获取默认字段列表
     */
    private List<String> getDefaultFields() {
        return List.of("changeCode", "title", "stage", "processorList", "teamName",
            "isUrgentChange", "priority", "createTime", "requesterName", "requesterId");
    }

    /**
     * 查询变更项数据
     */
    private Page<ChangeItemVo> queryChangeItems(ChangeItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ChangeItem> lqw = buildQueryWrapper(bo);
        String orderByColumn = pageQuery.getOrderByColumn();
        if (StringUtils.isBlank(orderByColumn)) {
            lqw.orderByDesc(ChangeItem::getId);
        }
        return changeItemMapper.selectVoPage(pageQuery.build(), lqw);
    }

    /**
     * 处理记录列表，进行字段过滤和额外数据填充
     */
    private List<ChangeItemVo> processRecords(List<ChangeItemVo> records, List<String> selectedFields) {
        // 判断是否需要查询额外字段
        boolean needExtraFields = needsExtraFields(selectedFields);

        // 如果需要额外字段，批量查询相关信息
        Map<Long, ChangeInfo> infoMap = needExtraFields ?
            buildChangeInfoMap(records) : Collections.emptyMap();

        // 过滤记录中的字段
        return records.stream()
            .map(item -> filterItemFields(item, selectedFields, infoMap))
            .toList();
    }

    /**
     * 判断是否需要查询额外字段
     */
    private boolean needsExtraFields(List<String> selectedFields) {
        List<String> extraFields = getExtraFieldsList();
        return selectedFields.stream().anyMatch(extraFields::contains);
    }

    /**
     * 获取需要额外查询的字段列表
     */
    private List<String> getExtraFieldsList() {
        return List.of("isNetworkFreezeChange", "applicationName", "locationName", CHANGE_TYPE_KEY, "frozen",
            "planTimeStart", "planTimeEnd", "affectedUser", "categoryNameList",
            "affectedApplicationName", "affectedDeviceName", "teamLeaderList",
            "changeApproverList", "applicationOwnerList", "changeImplementerList",
            "changeVerifierList", "urgentChangeInspectorList", "deptLeaderList",
            "systemsAffectedNo", "importantUsers", "fallback", "complexity",
            "riskLevel", "riskScore", "serviceDeliveryTeamList");
    }

    /**
     * 构建变更信息映射
     */
    private Map<Long, ChangeInfo> buildChangeInfoMap(List<ChangeItemVo> records) {
        return changeInfoMapper
            .selectByIds(records.stream().map(ChangeItemVo::getChangeId).toList())
            .stream()
            .collect(java.util.stream.Collectors.toMap(ChangeInfo::getId, info -> info, (k1, k2) -> k1));
    }

    /**
     * 过滤单个项目的字段
     */
    private ChangeItemVo filterItemFields(ChangeItemVo item, List<String> selectedFields, Map<Long, ChangeInfo> infoMap) {
        // 创建新对象
        ChangeItemVo filteredItem = new ChangeItemVo();

        // 设置必要字段
        setRequiredFields(filteredItem, item);

        // 设置基本字段
        setBasicFields(filteredItem, item, selectedFields);

        // 设置额外字段
        setExtraFields(filteredItem, item, selectedFields, infoMap);

        return filteredItem;
    }

    /**
     * 设置必要字段（总是需要的字段）
     */
    private void setRequiredFields(ChangeItemVo filteredItem, ChangeItemVo item) {
        filteredItem.setId(item.getId());
        filteredItem.setChangeId(item.getChangeId());
        filteredItem.setStage(item.getStage());
        filteredItem.setRequester(item.getRequester());
    }

    /**
     * 设置基本字段
     */
    private void setBasicFields(ChangeItemVo filteredItem, ChangeItemVo item, List<String> selectedFields) {
        if (selectedFields.contains("changeCode")) filteredItem.setChangeCode(item.getChangeCode());
        if (selectedFields.contains("title")) filteredItem.setTitle(item.getTitle());
        if (selectedFields.contains("processorList")) filteredItem.setProcessorList(item.getProcessorList());
        if (selectedFields.contains("teamName")) filteredItem.setTeamName(item.getTeamName());
        if (selectedFields.contains("isUrgentChange")) filteredItem.setIsUrgentChange(item.getIsUrgentChange());
        if (selectedFields.contains("priority")) filteredItem.setPriority(item.getPriority());
        if (selectedFields.contains("createTime")) filteredItem.setCreateTime(item.getCreateTime());
    }

    /**
     * 设置额外字段（需要从ChangeInfo查询的字段）
     */
    private void setExtraFields(ChangeItemVo filteredItem, ChangeItemVo item, List<String> selectedFields, Map<Long, ChangeInfo> infoMap) {
        Optional.ofNullable(infoMap.get(item.getChangeId())).ifPresent(info -> {
            setBasicExtraFields(filteredItem, info, selectedFields);
            setTimeAndUserFields(filteredItem, info, selectedFields);
            setUserListFields(filteredItem, info, selectedFields);
            setRiskAndComplexityFields(filteredItem, info, selectedFields);
        });
    }

    /**
     * 设置基本额外字段
     */
    private void setBasicExtraFields(ChangeItemVo filteredItem, ChangeInfo info, List<String> selectedFields) {
        if (selectedFields.contains("isNetworkFreezeChange"))
            filteredItem.setIsNetworkFreezeChange(info.getIsNetworkFreezeChange());
        if (selectedFields.contains("applicationName"))
            filteredItem.setApplicationName(info.getApplicationName());
        if (selectedFields.contains("locationName"))
            filteredItem.setLocationName(info.getLocationName());
        if (selectedFields.contains(CHANGE_TYPE_KEY))
            filteredItem.setChangeType(info.getChangeType());
        if (selectedFields.contains("frozen"))
            filteredItem.setIsNetworkFreezeChange(info.getIsNetworkFreezeChange());
        if (selectedFields.contains("categoryNameList"))
            filteredItem.setCategoryNameList(info.getCategoryNameList());
        if (selectedFields.contains("affectedApplicationName"))
            filteredItem.setAffectedApplicationName(info.getAffectedApplicationName());
        if (selectedFields.contains("affectedDeviceName"))
            filteredItem.setAffectedDeviceName(info.getAffectedDeviceName());
    }

    /**
     * 设置时间和用户相关字段
     */
    private void setTimeAndUserFields(ChangeItemVo filteredItem, ChangeInfo info, List<String> selectedFields) {
        if (selectedFields.contains("planTimeStart"))
            filteredItem.setPlanTimeStart(info.getPlanTimeStart());
        if (selectedFields.contains("planTimeEnd"))
            filteredItem.setPlanTimeEnd(info.getPlanTimeEnd());
        if (selectedFields.contains("affectedUser"))
            filteredItem.setAffectedUser(info.getAffectedUser());
        if (selectedFields.contains("importantUsers"))
            filteredItem.setImportantUsers(info.getImportantUsers());
        if (selectedFields.contains("fallback"))
            filteredItem.setFallback(info.getFallback());
    }

    /**
     * 设置用户列表相关字段
     */
    private void setUserListFields(ChangeItemVo filteredItem, ChangeInfo info, List<String> selectedFields) {
        if (selectedFields.contains("teamLeaderList"))
            filteredItem.setTeamLeaderList(info.getTeamLeaderList());
        if (selectedFields.contains("changeApproverList"))
            filteredItem.setChangeApproverList(info.getChangeApproverList());
        if (selectedFields.contains("applicationOwnerList"))
            filteredItem.setApplicationOwnerList(info.getApplicationOwnerList());
        if (selectedFields.contains("changeImplementerList"))
            filteredItem.setChangeImplementerList(info.getChangeImplementerList());
        if (selectedFields.contains("changeVerifierList"))
            filteredItem.setChangeVerifierList(info.getChangeVerifierList());
        if (selectedFields.contains("urgentChangeInspectorList"))
            filteredItem.setUrgentChangeInspectorList(info.getUrgentChangeInspectorList());
        if (selectedFields.contains("deptLeaderList"))
            filteredItem.setDeptLeaderList(info.getDeptLeaderList());
        if (selectedFields.contains("serviceDeliveryTeamList"))
            filteredItem.setServiceDeliveryTeamList(info.getServiceDeliveryTeamList());
    }

    /**
     * 设置风险和复杂度相关字段
     */
    private void setRiskAndComplexityFields(ChangeItemVo filteredItem, ChangeInfo info, List<String> selectedFields) {
        if (selectedFields.contains("systemsAffectedNo"))
            filteredItem.setSystemsAffectedNo(info.getSystemsAffectedNo());
        if (selectedFields.contains("complexity"))
            filteredItem.setComplexity(info.getComplexity());
        if (selectedFields.contains("riskLevel"))
            filteredItem.setRiskLevel(info.getRiskLevel());
        if (selectedFields.contains("riskScore"))
            filteredItem.setRiskScore(info.getRiskScore());
    }

    /**
     * 查询符合条件的变更申请列表
     *
     * @param bo 查询条件
     * @return 变更申请列表
     */
    @Override
    public List<ChangeInfoVo> queryList(ChangeInfoBo bo) {
        LambdaQueryWrapper<ChangeInfo> lqw = buildQueryWrapper(bo);
        return changeInfoMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChangeInfo> buildQueryWrapper(ChangeInfoBo bo) {
        LambdaQueryWrapper<ChangeInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ChangeInfo::getId);
        return lqw;
    }

    private LambdaQueryWrapper<ChangeItem> buildQueryWrapper(ChangeItemBo bo) {
        LambdaQueryWrapper<ChangeItem> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getChangeCode()), ChangeItem::getChangeCode, bo.getChangeCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), ChangeItem::getTitle, bo.getTitle());
        // lqw.like(StringUtils.isNotBlank(bo.getTeamName()), ChangeItem::getTeamName, bo.getTeamName());
        lqw.in(CollUtil.isNotEmpty(bo.getTeamIdList()), ChangeItem::getTeamId, bo.getTeamIdList());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_SEARCH(processor_list, 'one', CONCAT('%', {0}, '%'), NULL, '$[*].staffName') IS NOT NULL", bo.getProcessorName());
        lqw.in(CollUtil.isNotEmpty(bo.getStageList()), ChangeItem::getStage, bo.getStageList());
        lqw.in(CollUtil.isNotEmpty(bo.getIsUrgentChangeList()), ChangeItem::getIsUrgentChange, bo.getIsUrgentChangeList());
        lqw.in(CollUtil.isNotEmpty(bo.getIsNetworkFreezeChangeList()), ChangeItem::getIsNetworkFreezeChange, bo.getIsNetworkFreezeChangeList());
        lqw.apply(StringUtils.isNotBlank(bo.getRequesterName()), "JSON_EXTRACT(requester, '$.staffName') LIKE CONCAT('%', {0}, '%')", bo.getRequesterName());
        lqw.in(CollUtil.isNotEmpty(bo.getPriorityList()), ChangeItem::getPriority, bo.getPriorityList());
        lqw.in(CollUtil.isNotEmpty(bo.getLocationIdList()), ChangeItem::getLocationId, bo.getLocationIdList());
        lqw.gt(StringUtils.isNotBlank(bo.getCreateTimeStart()), ChangeItem::getCreateTime, bo.getCreateTimeStart());
        lqw.lt(StringUtils.isNotBlank(bo.getCreateTimeEnd()), ChangeItem::getCreateTime, bo.getCreateTimeEnd());

        // 添加草稿状态下，只有创建人能查看的条件
        LoginUser user = LoginHelper.getLoginUser();
        if (user != null && user.getStaffId() != null) {
            lqw.and(wrapper -> wrapper.ne(ChangeItem::getStage, ChangeStageEnum.DRAFT)
                .or()
                .eq(ChangeItem::getStage, ChangeStageEnum.DRAFT).eq(ChangeItem::getCreateBy, user.getUserId()));
        }

        return lqw;
    }

    /**
     * 新增变更申请
     *
     * @param bo 变更申请
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByBo(ChangeInfoBo bo) {
        // 获取当前登录用户
        LoginUser user = LoginHelper.getLoginUser();

        // 权限校验
        String requesterId = bo.getRequester().getStaffId();
        if (user.getStaffId() != null && !user.getStaffId().equals(requesterId)) {
            throw new ServiceException("当前用户不是变更申请人");
        }
        SysUser sysUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getStaffId, requesterId));

        // 转换BO为实体并校验
        ChangeInfo info = MapstructUtils.convert(bo, ChangeInfo.class);
        validEntityBeforeSave(info);

        // 保存变更申请基本信息
        int infoResult = changeInfoMapper.insert(info);
        if (infoResult <= 0) {
            throw new ServiceException("保存变更申请基本信息失败");
        }

        // 创建变更项
        ChangeItem item = BeanUtil.copyProperties(info, ChangeItem.class, "id");
        item.setChangeId(info.getId());
        item.setStage(bo.getStage());

        // 处理非草稿状态的流程
        ArrayList<ChangeRecord> records = new ArrayList<>();
        if (ChangeStageEnum.SUBMITTED.equals(bo.getStage())) {
            // 根据部门类型启动流程
            item.setSubmitTime(DateUtils.getNowDate());
            handleWorkflow(sysUser, bo, info, item, records);
        }

        // 生成变更编码（非草稿状态）
        if (!bo.getStage().equals(ChangeStageEnum.DRAFT)) {
            item.setChangeCode(generateChangeCode());
        }
        // 保存变更项信息
        int itemResult = changeItemMapper.insert(item);
        if (itemResult <= 0) {
            throw new ServiceException("保存变更申请项信息失败");
        }



        // 保存处理记录
        saveProcessRecords(item, records);

        return info.getId();
    }

    /**
     * 处理工作流
     */
    private void handleWorkflow(SysUser user, ChangeInfoBo bo, ChangeInfo info, ChangeItem item, ArrayList<ChangeRecord> records) {
        if (user.getDeptId().equals(1920403189165473793L)) {
            handleDsiWorkflow(user, bo, info, item, records);
        } else if (user.getDeptId().equals(1920403156194050049L)) {
            handleAppWorkflow(user, bo, info, item, records);
        } else if (user.getDeptId().equals(1920403120731209729L)) {
            handleInfraWorkflow(user, bo, info, item, records);
        }
    }

    /**
     * 处理DSI部门的工作流
     */
    private void handleDsiWorkflow(SysUser user, ChangeInfoBo bo, ChangeInfo info, ChangeItem item, ArrayList<ChangeRecord> records) {
        // 构建变量
        TreeMap<String, Object> variables = buildDsiVariables(user, bo);

        // 处理流程
        processWorkflow(user, bo, info, item, records, variables);
    }

    /**
     * 处理App部门的工作流
     */
    private void handleAppWorkflow(SysUser user, ChangeInfoBo bo, ChangeInfo info, ChangeItem item, ArrayList<ChangeRecord> records) {
        // 构建变量
        TreeMap<String, Object> variables = buildAppVariables(user, bo);

        // 处理流程
        processWorkflow(user, bo, info, item, records, variables);
    }

    /**
     * 处理Infra部门的工作流
     */
    private void handleInfraWorkflow(SysUser user, ChangeInfoBo bo, ChangeInfo info, ChangeItem item, ArrayList<ChangeRecord> records) {
        // 构建变量
        TreeMap<String, Object> variables = buildInfraVariables(user, bo);

        // 处理流程
        processWorkflow(user, bo, info, item, records, variables);
    }

    /**
     * 处理工作流通用流程
     */
    private void processWorkflow(SysUser user, ChangeInfoBo bo, ChangeInfo info, ChangeItem item,
                                 ArrayList<ChangeRecord> records, TreeMap<String, Object> variables) {
        // 获取下一节点列表
        FlowTaskNextListResp resp = smartFlowService.getNextList(String.valueOf(user.getUserId()), variables);

        // 创建处理记录和节点列表
        ArrayList<String> processorIdList = new ArrayList<>();
        ArrayList<String> processorNameList = new ArrayList<>();
        List<FlowInstanceStartResp.FlowInstanceNode> nodeList = new ArrayList<>();

        // 创建启动记录
        ChangeRecord startRecord = createStartRecord(user, info, item);
        records.add(startRecord);

        // 处理下一节点
        processNextNodes(resp, info, item, records, processorIdList, processorNameList, nodeList, startRecord);

        // 启动流程
        String instanceId = startFlow(user, item, info, nodeList, variables);

        // 更新变更项信息
        updateItemWithProcessors(item, instanceId, processorIdList, processorNameList, variables);
    }

    private List<Long> getUserIds(List<UserVo> list) {
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        return list.stream().map(UserVo::getUserId).toList();
    }

    /**
     * 构建DSI部门的变量
     */
    private TreeMap<String, Object> buildDsiVariables(SysUser user, ChangeInfoBo bo) {
        TreeMap<String, Object> variables = new TreeMap<>();
        variables.put(DSI_FIRST_REVIEWER_KEY, CollUtil.join(getUserIds(bo.getChangeVerifierList()), ","));
        variables.put(TEAM_MANAGER_KEY, CollUtil.join(getUserIds(bo.getChangeApproverList()), ","));
        variables.put(IMPLEMENTER_KEY, CollUtil.join(getUserIds(bo.getChangeImplementerList()), ","));
        variables.put(REVIEWER_KEY, CollUtil.join(getUserIds(bo.getChangeVerifierList()), ","));
        return variables;
    }

    /**
     * 构建App部门的变量
     */
    private TreeMap<String, Object> buildAppVariables(SysUser user, ChangeInfoBo bo) {
        TreeMap<String, Object> variables = new TreeMap<>();
        variables.put(CHANGE_TYPE_KEY, WhetherEnum.YES.equals(bo.getIsUrgentChange()) ? "1" : "0");
        variables.put(TEAM_MANAGER_KEY, CollUtil.join(getUserIds(bo.getChangeApproverList()), ","));
        variables.put(IMPLEMENTER_KEY, CollUtil.join(getUserIds(bo.getChangeImplementerList()), ","));
        variables.put(REVIEWER_KEY, CollUtil.join(getUserIds(bo.getChangeVerifierList()), ","));
        if (bo.getIsUrgentChange().equals(WhetherEnum.YES)) {
            variables.put(DEPT_HEAD_KEY, CollUtil.join(getUserIds(bo.getDeptLeaderList()), ","));
            variables.put(EMERGENCY_REVIEWER_KEY, CollUtil.join(getUserIds(bo.getUrgentChangeInspectorList()), ","));
        }
        if (bo.getIsUrgentChange().equals(WhetherEnum.NO)){
            //获取角色id是1927903567016935426 deptHead所有成员，并设置到变量中
            List<SysUserVo> roleUsers = userService.selectUserListByRoleId(1927903567016935426L);
            List<String> userIds = roleUsers.stream()
                .map(user -> user.getUserId().toString())
                .collect(Collectors.toList());
            variables.put(DEPT_HEAD_KEY, String.join(",", userIds));
        }
        variables.put(TEAM_LEADER_KEY, CollUtil.join(getUserIds(bo.getTeamLeaderList()), ","));
        return variables;
    }

    /**
     * 构建Infra部门的变量
     */
    private TreeMap<String, Object> buildInfraVariables(SysUser user, ChangeInfoBo bo) {
        TreeMap<String, Object> variables = new TreeMap<>();
        variables.put(CHANGE_TYPE_KEY, WhetherEnum.YES.equals(bo.getIsUrgentChange()) ? "1" : "0");
        variables.put(TEAM_MANAGER_KEY, CollUtil.join(getUserIds(bo.getChangeApproverList()), ","));
        variables.put(IMPLEMENTER_KEY, CollUtil.join(getUserIds(bo.getChangeImplementerList()), ","));
        variables.put(REVIEWER_KEY, CollUtil.join(getUserIds(bo.getChangeVerifierList()), ","));
        if (bo.getIsUrgentChange().equals(WhetherEnum.YES)) {
            variables.put(DEPT_HEAD_KEY, CollUtil.join(getUserIds(bo.getDeptLeaderList()), ","));
        }
        variables.put(TEAM_LEADER_KEY, CollUtil.join(getUserIds(bo.getTeamLeaderList()), ","));
        return variables;
    }

    /**
     * 创建启动记录
     */
    private ChangeRecord createStartRecord(SysUser user, ChangeInfo info, ChangeItem item) {
        ChangeRecord startRecord = new ChangeRecord();
        startRecord.setChangeId(info.getId());
        startRecord.setChangeCode(item.getChangeCode());
        startRecord.setTitle(info.getTitle());
        startRecord.setProcessorStatus(ProcessorStatusEnum.STARTED);
        startRecord.setProcessor(user.getUserVo());
        startRecord.setProcessOrder(1);
        startRecord.setStage(ChangeStageEnum.SUBMITTED);
        startRecord.setProcessorTime(new Date());
        startRecord.setTeamId(info.getTeamId());
        startRecord.setTeamName(info.getTeamName());
        startRecord.setIsUrgentChange(info.getIsUrgentChange());
        startRecord.setPriority(info.getPriority());
        startRecord.setIsNetworkFreezeChange(info.getIsNetworkFreezeChange());
        startRecord.setLocationId(info.getLocationId());
        startRecord.setLocationName(info.getLocationName());
        startRecord.setRequester(info.getRequester());
        startRecord.setNodeName("发起人");
        return startRecord;
    }

    /**
     * 处理下一节点
     */
    private void processNextNodes(FlowTaskNextListResp resp, ChangeInfo info, ChangeItem item,
                                  ArrayList<ChangeRecord> records, ArrayList<String> processorIdList,
                                  ArrayList<String> processorNameList, List<FlowInstanceStartResp.FlowInstanceNode> nodeList,
                                  ChangeRecord startRecord) {
        for (FlowTaskNextListResp.FlowTaskNext taskNext : resp.getData()) {
            FlowInstanceStartResp.FlowInstanceNode node = new FlowInstanceStartResp.FlowInstanceNode();
            node.setNodeId(taskNext.getId());
            node.setNodeName(taskNext.getName());

            // 设置处理人列表
            List<FlowInstanceStartResp.FlowInstanceHandler> handlerList = new ArrayList<>();

            if (taskNext.getCandidateList() != null) {
                for (FlowTaskNextListResp.Candidate handler : taskNext.getCandidateList()) {
                    handlerList.add(smartFlowService.createInstanceHandler(handler));

                    // 添加处理人ID和名称
                    processorIdList.add(handler.getAssigneeId());
                    processorNameList.add(handler.getAssigneeName());

                    // 添加待处理记录
                    ChangeRecord record = createPendingRecord(info, item, handler);
                    records.add(record);
                }
            }

            node.setHandlerList(handlerList);
            nodeList.add(node);
        }
    }

    /**
     * 创建待处理记录
     */
    private ChangeRecord createPendingRecord(ChangeInfo info, ChangeItem item, FlowTaskNextListResp.Candidate handler) {
        SysUser sysUser = userMapper.selectById(handler.getAssigneeId());

        ChangeRecord record = new ChangeRecord();
        record.setChangeId(info.getId());
        record.setChangeCode(item.getChangeCode());
        record.setTitle(info.getTitle());
        record.setProcessorStatus(ProcessorStatusEnum.PENDING);
        record.setProcessor(sysUser.getUserVo());
        record.setStage(ChangeStageEnum.SUBMITTED);
        record.setTeamId(info.getTeamId());
        record.setTeamName(info.getTeamName());
        record.setIsUrgentChange(info.getIsUrgentChange());
        record.setPriority(info.getPriority());
        record.setProcessOrder(2);
        record.setIsNetworkFreezeChange(info.getIsNetworkFreezeChange());
        record.setLocationId(info.getLocationId());
        record.setLocationName(info.getLocationName());
        record.setRequester(info.getRequester());
        return record;
    }

    /**
     * 启动流程
     */
    private String startFlow(SysUser user, ChangeItem item, ChangeInfo info,
                             List<FlowInstanceStartResp.FlowInstanceNode> nodeList, TreeMap<String, Object> variables) {
        return smartFlowService.startFlow(
            item.getChangeCode(),
            info.getTitle(),
            String.valueOf(user.getUserId()),
            user.getStaffName(),
            nodeList,
            variables
        );
    }

    /**
     * 更新变更项的处理人信息
     */
    private void updateItemWithProcessors(ChangeItem item, String instanceId, ArrayList<String> processorIdList,
                                          ArrayList<String> processorNameList, TreeMap<String, Object> variables) {
        item.setInstanceId(instanceId);
        List<SysUser> users = userMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, processorIdList));
        item.setProcessorList(users.stream().map(SysUser::getUserVo).toList());
        item.setVariables(JsonUtils.toJsonString(variables));
    }

    /**
     * 保存处理记录
     */
    private void saveProcessRecords(ChangeItem item, ArrayList<ChangeRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }

        for (ChangeRecord record : records) {
            record.setChangeItemId(item.getId());
            if (!record.getProcessorStatus().equals(ProcessorStatusEnum.STARTED)) {
                populateRecordWithTaskInfo(item, record);
            }
            changeRecordMapper.insert(record);
        }
        records.parallelStream()
            .filter(record -> ProcessorStatusEnum.PENDING.equals(record.getProcessorStatus()))
            .forEach(record -> sendEmailAsync(record.getId(), record.getChangeId(), record.getProcessor().getUserId()));
    }

    /**
     * 填充记录的任务信息
     */
    private void populateRecordWithTaskInfo(ChangeItem item, ChangeRecord record) {
        try {
            String processorId = String.valueOf(record.getProcessor().getUserId());
            // 获取当前执行任务
            FlowTaskCurrentResp currentTask = getCurrentTask(item, processorId);
            record.setTaskId(String.valueOf(currentTask.getData().getId()));
            record.setTaskCurrent(JsonUtils.toJsonString(currentTask));
            record.setNodeName(currentTask.getData().getNodeName());

            // 获取下一节点列表
            TreeMap<String, Object> vars = JSONUtil.toBean(item.getVariables(), TreeMap.class);
            FlowTaskNextListResp resp3 = smartFlowService.getNextList(processorId, record.getTaskId(), vars);
            record.setNextList(JsonUtils.toJsonString(resp3));

            // 获取任务处理策略
            FlowTaskStrategyResp resp4 = smartFlowService.getStrategy(processorId, record.getTaskId());
            record.setStrategy(JsonUtils.toJsonString(resp4));
            record.setCountersignType(resp4.getData().getCountersignType());
            record.setCountersign(resp4.getData().getCountersign());
        } catch (Exception e) {
            log.error("填充任务信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取当前任务
     */
    private FlowTaskCurrentResp getCurrentTask(ChangeItem item, String staffId) {
        return smartFlowService.getCurrentTask(item.getInstanceId(), staffId);
    }

    /**
     * 创建流程实例处理人
     */
    private FlowInstanceStartResp.FlowInstanceHandler createInstanceHandler(FlowTaskNextListResp.Candidate candidate) {
        return smartFlowService.createInstanceHandler(candidate);
    }

    /**
     * 设置变更编号
     * 格式：CM-YYYYMM-XXX
     * YYYYMM:年月，例如202412
     * XXX:每月从001开始递增的序列号
     */
    private String generateChangeCode() {
        // 获取当前年月作为前缀
        String yearMonth = DateUtil.format(DateUtil.date(), "yyyyMM");
        // Redis中的key，用于存储序列号
        String redisKey = "CM-" + yearMonth;
        // 使用SequenceUtils生成序列号，每月重置，从1开始，步长为1
        long sequence = SequenceUtils.nextId(redisKey, Duration.ofDays(31), 1L, 1L);
        // 格式化序列号为3位数，例如：001, 010, 100
        String sequenceStr = String.format("%03d", sequence);
        // 设置完整的freezeCode
        return "CM-" + yearMonth + "-" + sequenceStr;
    }

    /**
     * 修改变更申请
     *
     * @param bo 变更申请
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ChangeInfoBo bo) {
        // 获取当前登录用户和变更项
//        LoginUser user = LoginHelper.getLoginUser();
        ChangeItem item = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>().eq(ChangeItem::getChangeId, bo.getId()));

        // 状态检查
        if (!List.of(ChangeStageEnum.DRAFT, ChangeStageEnum.SUBMITTED).contains(item.getStage())) {
            throw new ServiceException("当前状态不能修改变更申请");
        }

        // 获取旧数据并转换BO为实体
        ChangeInfo oldInfo = changeInfoMapper.selectById(bo.getId());
        ChangeInfo newInfo = MapstructUtils.convert(bo, ChangeInfo.class);
        validEntityBeforeSave(newInfo);

        // 更新变更项基本信息
        BeanUtil.copyProperties(newInfo, item, "id");

        ChangeRecord changeRecord = new ChangeRecord();
        changeRecord.setChangeCode(item.getChangeCode());
        changeRecord.setTitle(item.getTitle());
        changeRecord.setTeamId(item.getTeamId());
        changeRecord.setTeamName(item.getTeamName());
        changeRecord.setIsUrgentChange(item.getIsUrgentChange());
        changeRecord.setPriority(item.getPriority());
        changeRecord.setIsNetworkFreezeChange(item.getIsNetworkFreezeChange());
        changeRecord.setLocationId(item.getLocationId());
        changeRecord.setLocationName(item.getLocationName());
        changeRecord.setRequester(item.getRequester());
        changeRecordMapper.update(changeRecord, new LambdaUpdateWrapper<ChangeRecord>().eq(ChangeRecord::getChangeItemId, item.getId()));

        // 更新变更信息并记录变更历史
        boolean result = changeInfoMapper.updateById(newInfo) > 0;
        // compareAndLogChangeInfo(oldInfo, newInfo, item.getChangeCode());

        SysUser reqUser = userMapper.selectOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getStaffId, bo.getRequester().getStaffId()));
        // 处理状态变化
        ArrayList<ChangeRecord> records = new ArrayList<>();
        if (item.getStage().equals(ChangeStageEnum.DRAFT) && ChangeStageEnum.SUBMITTED.equals(bo.getStage())) {
            item.setStage(bo.getStage());
            item.setSubmitTime(DateUtils.getNowDate());
            // 从草稿状态提交，启动流程
            handleWorkflow(reqUser, bo, newInfo, item, records);
        } else if (item.getStage().equals(ChangeStageEnum.SUBMITTED) && ChangeStageEnum.SUBMITTED.equals(bo.getStage())) {
            // 更新已提交状态的变量
            updateWorkflowVariables(reqUser, bo, item);
        }

        // 生成变更编码（如需要）
        if (StringUtils.isBlank(item.getChangeCode()) && !bo.getStage().equals(ChangeStageEnum.DRAFT)) {
            item.setChangeCode(generateChangeCode());
        }

        // 更新变更项
        int itemResult = changeItemMapper.updateById(item);
        if (itemResult <= 0) {
            throw new ServiceException("保存变更申请项信息失败");
        }

        // 保存处理记录
        saveProcessRecords(item, records);

        return result;
    }

    /**
     * 更新工作流变量
     */
    private void updateWorkflowVariables(SysUser user, ChangeInfoBo bo, ChangeItem item) {
        TreeMap<String, Object> variables = new TreeMap<>();

        if (user.getDeptId().equals(1920403189165473793L)) {
            variables = buildDsiVariables(user, bo);
        } else if (user.getDeptId().equals(1920403156194050049L)) {
            variables = buildAppVariables(user, bo);
        } else if (user.getDeptId().equals(1920403120731209729L)) {
            variables = buildInfraVariables(user, bo);
        }

        // 更新变量
        item.setVariables(JsonUtils.toJsonString(variables));
    }

    /**
     * 比较并记录ChangeInfo的变更内容
     *
     * @param oldInfo    原始变更信息
     * @param newInfo    新的变更信息
     * @param changeCode 变更code
     */
    private void compareAndLogChangeInfo(ChangeInfo oldInfo, ChangeInfo newInfo, String changeCode) {
        if (oldInfo == null || newInfo == null || StringUtils.isBlank(changeCode)) {
            return;
        }

        try {
            Set<String> ignoreFields = new HashSet<>(Arrays.asList(
                "changeId", "version", "delFlag", "remark", "updateBy", "updateTime", "params",
                "codeReviewFileIds", "affectedDeviceIds", "requestDocFileIds", "testDocFileIds", "affectedApplicationIds", "categoryIds"));

            LoginUser loginUser = LoginHelper.getLoginUser();

            modifyLogService.compareAndLogDifferences(oldInfo, newInfo, changeCode, ignoreFields, loginUser);
        } catch (Exception e) {
            log.error("记录ChangeInfo变更历史异常: {}", e.getMessage());
        }
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChangeInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除变更申请信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return changeInfoMapper.deleteByIds(ids) > 0;
    }

    /**
     * DSI阶段流转
     * 根据上一阶段、节点名和审批状态确定下一阶段
     *
     * @param changeRecord    上一阶段
     * @param nodeName        当前节点名称，作为阶段跳转的判断条件
     * @param processorStatus 审批状态
     * @return 下一阶段
     */
    private ChangeStageEnum dsiStageTransition(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();
        // 先处理DSI特有的阶段流转逻辑
        if (ChangeStageEnum.SUBMITTED.equals(previousStage)) {
            if (ProcessorStatusEnum.APPROVED.equals(processorStatus) && NODE_REVIEWER.equals(nodeName)) {
                return ChangeStageEnum.PENDING_APPROVAL;
            } else if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
                return ChangeStageEnum.REJECTED;
            }
        } else if (ChangeStageEnum.PENDING_APPROVAL.equals(previousStage)) {
            if (ProcessorStatusEnum.APPROVED.equals(processorStatus) && NODE_TEAM_MANAGER_APPROVAL.equals(nodeName)) {
                return ChangeStageEnum.APPROVED;
            } else if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
                return ChangeStageEnum.REJECTED;
            }
        }

        // 处理通用的阶段流转逻辑
        return handleCommonStageTransition(changeRecord, nodeName, processorStatus);
    }

    /**
     * App阶段流转
     * 根据上一阶段、节点名、审批状态和是否紧急变更确定下一阶段
     *
     * @param changeRecord    上一阶段
     * @param nodeName        当前节点名称，作为阶段跳转的判断条件
     * @param processorStatus 审批状态
     * @param isUrgentChange  是否紧急变更
     * @return 下一阶段
     */
    private ChangeStageEnum appStageTransition(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus, WhetherEnum isUrgentChange) {
        // 处理拒绝状态 - 所有流程都返回REJECTED
        if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
            return ChangeStageEnum.REJECTED;
        }

        // 根据是否紧急变更选择不同的处理流程
        if (WhetherEnum.YES.equals(isUrgentChange)) {
            return handleAppUrgentChangeFlow(changeRecord, nodeName, processorStatus);
        } else {
            return handleAppNormalChangeFlow(changeRecord, nodeName, processorStatus);
        }
    }

    /**
     * 处理App紧急变更流程
     */
    private ChangeStageEnum handleAppUrgentChangeFlow(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();

        if (ChangeStageEnum.SUBMITTED.equals(previousStage)) {
            ChangeStageEnum result = handleAppUrgentSubmittedStage(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        if (ChangeStageEnum.PENDING_APPROVAL.equals(previousStage)) {
            ChangeStageEnum result = handleAppUrgentPendingApprovalStage(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        // 处理通用的阶段流转逻辑
        return handleCommonStageTransition(changeRecord, nodeName, processorStatus);
    }

    /**
     * 处理App普通变更流程
     */
    private ChangeStageEnum handleAppNormalChangeFlow(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();

        if (ChangeStageEnum.SUBMITTED.equals(previousStage)) {
            ChangeStageEnum result = handleAppNormalSubmittedStage(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        if (ChangeStageEnum.PENDING_APPROVAL.equals(previousStage)) {
            ChangeStageEnum result = handleAppNormalPendingApprovalStage(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        // 处理通用的阶段流转逻辑
        return handleCommonStageTransition(changeRecord, nodeName, processorStatus);
    }

    /**
     * 处理App紧急变更的SUBMITTED阶段
     */
    private ChangeStageEnum handleAppUrgentSubmittedStage(String nodeName, ProcessorStatusEnum processorStatus) {
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus) && NODE_EMERGENCY_REVIEWER.equals(nodeName)) {
            return ChangeStageEnum.PENDING_APPROVAL;
        }
        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理App紧急变更的PENDING_APPROVAL阶段
     */
    private ChangeStageEnum handleAppUrgentPendingApprovalStage(String nodeName, ProcessorStatusEnum processorStatus) {
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus) && NODE_DEPT_LEADER_APPROVAL.equals(nodeName)) {
            return ChangeStageEnum.APPROVED;
        }
        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理App普通变更的SUBMITTED阶段
     */
    private ChangeStageEnum handleAppNormalSubmittedStage(String nodeName, ProcessorStatusEnum processorStatus) {
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus)) {
            if (NODE_TEAM_LEADER_APPROVAL.equals(nodeName)) {
                return ChangeStageEnum.SUBMITTED;
            }
            if (NODE_VIRTUAL_GROUP_APPROVAL.equals(nodeName)) {
                return ChangeStageEnum.PENDING_APPROVAL;
            }
        }
        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理App普通变更的PENDING_APPROVAL阶段
     */
    private ChangeStageEnum handleAppNormalPendingApprovalStage(String nodeName, ProcessorStatusEnum processorStatus) {
        if (ProcessorStatusEnum.APPROVED.equals(processorStatus) && NODE_TEAM_MANAGER_APPROVAL.equals(nodeName)) {
            return ChangeStageEnum.APPROVED;
        }
        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * Infra阶段流转
     * 根据上一阶段、节点名、审批状态和是否紧急变更确定下一阶段
     *
     * @param changeRecord    上一阶段
     * @param nodeName        当前节点名称，作为阶段跳转的判断条件
     * @param processorStatus 审批状态
     * @param isUrgentChange  是否紧急变更
     * @return 下一阶段
     */
    private ChangeStageEnum infraStageTransition(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus, WhetherEnum isUrgentChange) {
        // 处理拒绝状态 - 所有流程都返回REJECTED
        if (ProcessorStatusEnum.REJECTED.equals(processorStatus)) {
            return ChangeStageEnum.REJECTED;
        }

        // 根据是否紧急变更选择不同的处理流程
        if (WhetherEnum.YES.equals(isUrgentChange)) {
            return handleInfraUrgentChangeFlow(changeRecord, nodeName, processorStatus);
        } else {
            return handleInfraNormalChangeFlow(changeRecord, nodeName, processorStatus);
        }
    }

    /**
     * 处理Infra紧急变更流程
     */
    private ChangeStageEnum handleInfraUrgentChangeFlow(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();

        if (ChangeStageEnum.SUBMITTED.equals(previousStage) &&
            ProcessorStatusEnum.APPROVED.equals(processorStatus) &&
            NODE_DEPT_LEADER_APPROVAL.equals(nodeName)) {
            return ChangeStageEnum.APPROVED;
        }

        // 处理通用的阶段流转逻辑
        return handleCommonStageTransition(changeRecord, nodeName, processorStatus);
    }

    /**
     * 处理Infra普通变更流程
     */
    private ChangeStageEnum handleInfraNormalChangeFlow(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();

        if (ChangeStageEnum.SUBMITTED.equals(previousStage) &&
            ProcessorStatusEnum.APPROVED.equals(processorStatus) &&
            NODE_TEAM_LEADER_APPROVAL.equals(nodeName)) {
            return ChangeStageEnum.PENDING_APPROVAL;
        }

        if (ChangeStageEnum.PENDING_APPROVAL.equals(previousStage) &&
            ProcessorStatusEnum.APPROVED.equals(processorStatus) &&
            NODE_TEAM_MANAGER_APPROVAL.equals(nodeName)) {
            return ChangeStageEnum.APPROVED;
        }

        // 处理通用的阶段流转逻辑
        return handleCommonStageTransition(changeRecord, nodeName, processorStatus);
    }

    /**
     * 处理通用的阶段流转逻辑
     * 适用于所有流程中共有的阶段流转规则
     *
     * @param changeRecord    上一阶段
     * @param nodeName        当前节点名称
     * @param processorStatus 处理状态
     * @return 下一阶段
     */
    private ChangeStageEnum handleCommonStageTransition(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        ChangeStageEnum previousStage = changeRecord.getStage();

        if (ChangeStageEnum.APPROVED.equals(previousStage)) {
            ChangeStageEnum result = handleApprovedStageTransition(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        if (ChangeStageEnum.IMPLEMENTING.equals(previousStage)) {
            ChangeStageEnum result = handleImplementingStageTransition(nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        if (ChangeStageEnum.PENDING_VERIFICATION.equals(previousStage)) {
            ChangeStageEnum result = handlePendingVerificationStageTransition(changeRecord, nodeName, processorStatus);
            if (result != null) {
                return result;
            }
        }

        throw new ServiceException("未找到流转状态");
    }

    /**
     * 处理已审批阶段的流转逻辑
     */
    private ChangeStageEnum handleApprovedStageTransition(String nodeName, ProcessorStatusEnum processorStatus) {
        if (NODE_IMPLEMENTER.equals(nodeName) && ProcessorStatusEnum.START_CHANGE.equals(processorStatus)) {
            return ChangeStageEnum.IMPLEMENTING;
        }
        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理实施中阶段的流转逻辑
     */
    private ChangeStageEnum handleImplementingStageTransition(String nodeName, ProcessorStatusEnum processorStatus) {
        if (!NODE_IMPLEMENTING.equals(nodeName)) {
            return null; // 节点名称不匹配
        }

        if (ProcessorStatusEnum.IMPLEMENTED.equals(processorStatus) ||
            ProcessorStatusEnum.ROLLED_BACK.equals(processorStatus)) {
            return ChangeStageEnum.PENDING_VERIFICATION;
        }

        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理待验证阶段的流转逻辑
     */
    private ChangeStageEnum handlePendingVerificationStageTransition(ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        if (ProcessorStatusEnum.NOT_PASS.equals(processorStatus)) {
            return ChangeStageEnum.IMPLEMENTING;
        }

        if (ProcessorStatusEnum.PASS.equals(processorStatus) && NODE_REVIEWER.equals(nodeName)) {
            return handleVerificationPassTransition(changeRecord);
        }

        return null; // 未匹配的情况返回null，由调用方处理
    }

    /**
     * 处理验证通过的流转逻辑
     */
    private ChangeStageEnum handleVerificationPassTransition(ChangeRecord changeRecord) {
        boolean hasRollbackRecord = checkForRollbackRecord(changeRecord);
        return hasRollbackRecord ? ChangeStageEnum.ROLLED_BACK : ChangeStageEnum.COMPLETED;
    }

    /**
     * 检查是否存在回滚记录
     */
    private boolean checkForRollbackRecord(ChangeRecord changeRecord) {
        return changeRecordMapper.exists(new LambdaQueryWrapper<ChangeRecord>()
            .eq(ChangeRecord::getChangeItemId, changeRecord.getChangeItemId())
            .eq(ChangeRecord::getProcessOrder, changeRecord.getProcessOrder() - 1)
            .eq(ChangeRecord::getStage, ProcessorStatusEnum.IMPLEMENTED)
            .eq(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.ROLLED_BACK)
        );
    }

    /**
     * 更新变更项的处理人信息
     */
    private void updateItemProcessors(ChangeItem item, List<ChangeRecord> newRecords) {
        // 获取当前处理人ID列表
//        List<String> processorIds = new ArrayList<>();
//        List<String> processorNames = new ArrayList<>();
//
//        if (CollUtil.isNotEmpty(item.getProcessorList())){
//            processorIds.addAll(item.getProcessorList().stream().map(UserVo::getUserId).map(String::valueOf).toList());
//            processorNames.addAll(item.getProcessorList().stream().map(UserVo::getStaffName).toList());
//
//        }
//
//        // 添加新处理人
//        for (ChangeRecord record : newRecords) {
//            if (!processorIds.contains(record.getProcessor().getUserId())) {
//                processorIds.add(record.getProcessorId());
//                processorNames.add(record.getProcessorName());
//            }
//        }

        List<UserVo> processorList = item.getProcessorList();
        for (ChangeRecord record : newRecords) {
            if (!processorList.contains(record.getProcessor())) {
                processorList.add(record.getProcessor());
            }
        }
        item.setProcessorList(processorList);
        changeItemMapper.updateById(item);
    }

    /**
     * 取消变更流程
     *
     * @param bo 取消变更请求参数
     * @return 是否取消成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelChange(ChangeCancelBo bo) {
        Long infoId = bo.getInfoId();
        // 获取当前登录用户
        LoginUser user = LoginHelper.getLoginUser();

        // 查询变更申请信息
        ChangeInfo info = changeInfoMapper.selectById(infoId);
        if (info == null) {
            throw new ServiceException("变更申请不存在");
        }

        // 验证是否为创建人
        if (!info.getRequester().getStaffId().equals(user.getStaffId())) {
            throw new ServiceException("只有变更申请创建人才能取消流程");
        }

        // 查询变更项
        ChangeItem item = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>()
            .eq(ChangeItem::getChangeId, infoId));

        if (item == null) {
            throw new ServiceException("变更项不存在");
        }

        // 更新变更项状态为已取消
        item.setStage(ChangeStageEnum.CANCELLED);
        item.setCompleteTime(new Date());
        changeItemMapper.updateById(item);

        // 查询待审批记录
        List<ChangeRecord> records = changeRecordMapper.selectList(new LambdaQueryWrapper<ChangeRecord>()
            .eq(ChangeRecord::getChangeId, infoId)
            .eq(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING));

        if (!records.isEmpty()) {
            // 更新所有待审批记录为已取消
            for (ChangeRecord record : records) {
                record.setProcessorStatus(ProcessorStatusEnum.CANCELLED);
                record.setOpinion("流程已被创建人取消");
                changeRecordMapper.updateById(record);
            }
        }

        // 如果有流程实例ID，调用SmartFlow取消流程
        if (StringUtils.isNotBlank(item.getInstanceId())) {
            try {
                smartFlowService.cancelFlow(
                    item.getInstanceId(),
                    String.valueOf(user.getUserId()),
                    "用户主动取消流程"
                );
            } catch (Exception e) {
                log.error("调用SmartFlow取消流程失败", e);
                throw new ServiceException("取消流程失败: " + e.getMessage());
            }
        }
        // 异步发送邮件通知
        sendEmailAsync(ChangeStageEnum.CANCELLED, item.getChangeId());

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByBo(ChangeRecordBo2 bo) {
        // 获取当前审批记录
        ChangeRecord record = changeRecordMapper.selectById(bo.getId());
        if (record == null) {
            throw new ServiceException("审批记录不存在");
        }

        if (ChangeStageEnum.PENDING_VERIFICATION.equals(record.getStage()) && ProcessorStatusEnum.PASS.equals(bo.getProcessorStatus())) {
            List<ChangeDelayVo> changeDelayVos = changeDelayMapper.selectVoList(new LambdaQueryWrapper<ChangeDelay>()
                .eq(ChangeDelay::getChangeId, record.getChangeId()));
            //判断ChangeDelayVos里delayFiles是否全部上传
            boolean hasEmptyDelayFiles = changeDelayVos.stream()
                .anyMatch(delayVo -> delayVo.getDelayFiles() == null || delayVo.getDelayFiles().isEmpty());
            if (hasEmptyDelayFiles) {
                throw new ServiceException("存在未上传延期文件，请确保所有延期记录都已上传文件");
            }
        }

        // 获取变更项信息
        ChangeItem item = changeItemMapper.selectById(record.getChangeItemId());
        if (item == null) {
            throw new ServiceException("变更项不存在");
        }

        // 处理加签逻辑
        if (WhetherEnum.YES.equals(bo.getIsSealAddition()) && !CollUtil.isEmpty(bo.getSealAdditionList())) {
            handleSealAddition(record, item, bo.getSealAdditionList().stream().map(UserVo::getUserId).map(String::valueOf).toList());
        }


        Integer signType = record.getCountersignType();
        if (signType == null) {
            throw new ServiceException("未设置签署类型");
        }

        if (signType == 0) {
            handleCounterSignLogic(record, bo, item);
        } else {
            handleOrSignLogic(record, bo, item);
        }

    }

    /**
     * 处理加签逻辑
     */
    private void handleSealAddition(ChangeRecord currentRecord, ChangeItem item, List<String> sealAdditionList) {
        // 为每个加签人创建待处理记录
        if (CollUtil.isEmpty(sealAdditionList)) {
            return;
        }

        List<ChangeRecord> records = new ArrayList<>();

        // 通过userId获取用户名
        Map<Long, String> userMap = userMapper.selectList(new LambdaQueryWrapper<SysUser>()
                .in(SysUser::getUserId, sealAdditionList))
            .stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getStaffName, (l, r) -> l));

        List<FlowTaskHandlerReqVO> handlerList = new ArrayList<>();
        for (String assigneeId : sealAdditionList) {
            FlowTaskHandlerReqVO reqVO = new FlowTaskHandlerReqVO();
            reqVO.setAssigneeId(assigneeId);
            reqVO.setAssigneeName(userMap.get(Long.valueOf(assigneeId)));
            reqVO.setAssigneeOrgId(cecSmartFlowConfig.getParam().getDeptId());
            reqVO.setAssigneeOrgName(cecSmartFlowConfig.getParam().getDeptName());
            handlerList.add(reqVO);
        }

        // 执行加签
        smartFlowService.countersign(
            currentRecord.getTaskId(),
            String.valueOf(currentRecord.getProcessor().getUserId()),
            handlerList,
            0
        );

        // 为每个加签人创建待处理记录
        for (String assigneeId : sealAdditionList) {
            // 创建处理记录
            ChangeRecord record = createChangeRecordForProcessor(
                item,
                currentRecord.getChangeId(),
                currentRecord.getTitle(),
                currentRecord.getStage(),
                assigneeId,
                userMap.get(assigneeId),
                currentRecord.getTeamId(),
                currentRecord.getTeamName(),
                currentRecord.getIsUrgentChange(),
                currentRecord.getPriority(),
                currentRecord.getProcessOrder(),
                true
            );

            // 填充任务信息
            populateRecordWithTaskInfo(record, item, assigneeId);

            // 保存加签记录
            changeRecordMapper.insert(record);
            records.add(record);
        }

        // 更新变更项的处理人信息
        updateItemProcessors(item, records);
    }

    /**
     * 填充记录的任务信息
     */
    private void populateRecordWithTaskInfo(ChangeRecord record, ChangeItem item, String processorId) {
        try {
            // 获取当前执行任务
            FlowTaskCurrentResp currentTask = smartFlowService.getCurrentTask(item.getInstanceId(), processorId);
            if (currentTask != null && currentTask.getData() != null) {
                record.setTaskId(String.valueOf(currentTask.getData().getId()));
                record.setTaskCurrent(JsonUtils.toJsonString(currentTask));
                record.setNodeName(currentTask.getData().getNodeName());

                // 获取下一节点列表
                TreeMap<String, Object> vars = JSONUtil.toBean(item.getVariables(), TreeMap.class);
                FlowTaskNextListResp resp3 = smartFlowService.getNextList(processorId, record.getTaskId(), vars);
                record.setNextList(JsonUtils.toJsonString(resp3));

                // 获取任务处理策略
                FlowTaskStrategyResp resp4 = smartFlowService.getStrategy(processorId, record.getTaskId());
                record.setStrategy(JsonUtils.toJsonString(resp4));
                record.setCountersignType(resp4.getData().getCountersignType());
                record.setCountersign(resp4.getData().getCountersign());
            }
        } catch (Exception e) {
            log.error("获取任务信息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 创建处理人的变更记录
     */
    private ChangeRecord createChangeRecordForProcessor(
        ChangeItem item, Long changeId, String title, ChangeStageEnum stage,
        String processorId, String processorName, Long teamId, String teamName,
        WhetherEnum isUrgentChange, LevelEnum priority, Integer order, Boolean isSealAddition) {

        SysUser sysUser = userMapper.selectById(processorId);

        ChangeRecord record = new ChangeRecord();
        record.setChangeId(changeId);
        record.setChangeItemId(item.getId());
        record.setChangeCode(item.getChangeCode());
        record.setTitle(title);
        record.setProcessorStatus(ProcessorStatusEnum.PENDING);
        record.setProcessor(sysUser.getUserVo());
        record.setStage(stage);
        record.setTeamId(teamId);
        record.setTeamName(teamName);
        record.setIsUrgentChange(isUrgentChange);
        record.setPriority(priority);
        record.setProcessOrder(order);
        record.setIsNetworkFreezeChange(item.getIsNetworkFreezeChange());
        record.setLocationId(item.getLocationId());
        record.setLocationName(item.getLocationName());
        record.setRequester(item.getRequester());
        if (isSealAddition) {
            record.setIsSealAddition(WhetherEnum.YES);
        }
        return record;
    }

    /**
     * 处理会签逻辑
     * 会签需要所有审批人都通过，任一人拒绝则整个流程拒绝
     */
    private void handleCounterSignLogic(ChangeRecord changeRecord, ChangeRecordBo2 bo, ChangeItem item) {

        FlowTaskCurrentResp taskCurrent = smartFlowService.parseTaskCurrent(changeRecord.getTaskCurrent());
        String nodeName = getNodeNameFromTaskCurrent(taskCurrent);
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, bo.getProcessorStatus());

        // 处理流程任务
        handleFlowTask(changeRecord, bo, item, taskCurrent);

        // 移除当前处理人
        removeCurrentProcessor(item, changeRecord);

        // 更新当前记录的状态
        updateChangeRecordStatus(changeRecord, bo);

        // 处理NOT_PASS状态的特殊情况（PENDING_VERIFICATION阶段）
        if (isNotPassInVerificationStage(changeRecord, bo)) {
            handleNotPassInVerificationStage(changeRecord, bo, item, nodeName);
            return;
        }

        // 处理拒绝状态
        if (ProcessorStatusEnum.REJECTED.equals(bo.getProcessorStatus())) {
            handleRejection(changeRecord, item, nodeName);
            return;
        }

        // 检查是否所有审批人都已处理
        if (hasPendingRecords(changeRecord)) {
            return; // 还有待处理记录，等待其他人审批
        }

        FlowTaskNextListResp nextListResp = smartFlowService.parseNextList(changeRecord.getNextList());
        if (WhetherEnum.YES.equals(changeRecord.getIsSealAddition())) {
            List<ChangeRecord> changeRecords = changeRecordMapper.selectList(new LambdaQueryWrapper<>(ChangeRecord.class)
                .eq(ChangeRecord::getChangeItemId, changeRecord.getChangeItemId())
                .isNull(ChangeRecord::getIsSealAddition)
                .ne(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING)
                .eq(ChangeRecord::getProcessOrder, changeRecord.getProcessOrder())
            );
            if (CollUtil.isNotEmpty(changeRecords)) {

                ChangeRecord record = changeRecords.stream()
                    .max(Comparator.comparing(ChangeRecord::getId)).orElse(null);
                if (ObjectUtil.isNotNull(record)) {
                    nextListResp = smartFlowService.parseNextList(record.getNextList());
                }
            }
        }

        // 所有人都已审批通过，流转到下一阶段
        transitionToNextStage(changeRecord, item, nodeName, bo.getProcessorStatus(), nextListResp);
    }

    /**
     * 从处理人列表中移除当前审批人
     *
     * @param item   项
     * @param record 处理记录
     */
    private void removeCurrentProcessor(ChangeItem item, ChangeRecord record) {
        Long currentUserId = record.getProcessor().getUserId();
        if (item.getProcessorList() != null) {
            List<UserVo> updatedProcessorList = item.getProcessorList().stream()
                .filter(userVo -> !userVo.getUserId().equals(currentUserId))
                .collect(Collectors.toList());
            item.setProcessorList(updatedProcessorList);
            changeItemMapper.updateById(item);
        }
    }

    /**
     * 处理或签逻辑
     * 或签只需一人通过即可，所有人拒绝才算拒绝
     */
    private void handleOrSignLogic(ChangeRecord changeRecord, ChangeRecordBo2 bo, ChangeItem item) {
        // 更新当前记录的状态
        updateChangeRecordStatus(changeRecord, bo);

        FlowTaskCurrentResp taskCurrent = smartFlowService.parseTaskCurrent(changeRecord.getTaskCurrent());
        FlowTaskNextListResp nextListResp = smartFlowService.parseNextList(changeRecord.getNextList());
        String nodeName = getNodeNameFromTaskCurrent(taskCurrent);

        //先判断是否有下一步处理状态
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, bo.getProcessorStatus());
        // 处理流程任务
        handleFlowTask(changeRecord, bo, item, taskCurrent);

        // 处理NOT_PASS状态的特殊情况（PENDING_VERIFICATION阶段）
        if (isNotPassInVerificationStage(changeRecord, bo)) {
            handleNotPassInVerificationStage(changeRecord, bo, item, nodeName);
            return;
        }

        // 处理通过状态（PENDING_VERIFICATION阶段的PASS或普通审批通过）
        if ((ChangeStageEnum.PENDING_VERIFICATION.equals(changeRecord.getStage()) && ProcessorStatusEnum.PASS.equals(bo.getProcessorStatus())) ||
            ProcessorStatusEnum.APPROVED.equals(bo.getProcessorStatus())) {
            handleApproval(changeRecord, item, nodeName, bo.getProcessorStatus(), nextListResp);
            return;
        }

        // 处理拒绝状态 - 检查是否所有人都拒绝了
        if (ProcessorStatusEnum.REJECTED.equals(bo.getProcessorStatus()) ||
            ProcessorStatusEnum.NOT_PASS.equals(bo.getProcessorStatus())) {

            if (!hasPendingRecords(changeRecord)) {
                // 所有人都已拒绝，流转到拒绝阶段
                item.setStage(ChangeStageEnum.REJECTED);
                item.setProcessorList(List.of());
                changeItemMapper.updateById(item);
            }
        }

    }

    /**
     * 更新变更记录状态
     */
    private void updateChangeRecordStatus(ChangeRecord changeRecord, ChangeRecordBo2 bo) {
        changeRecord.setProcessorStatus(bo.getProcessorStatus());
        changeRecord.setProcessorTime(new Date());
        changeRecord.setOpinion(bo.getOpinion());
        changeRecordMapper.updateById(changeRecord);
    }

    /**
     * 从当前任务中获取节点名称
     */
    private String getNodeNameFromTaskCurrent(FlowTaskCurrentResp currentResp) {
        return ObjectUtil.isEmpty(currentResp.getData()) ? "" : currentResp.getData().getNodeName();
    }

    /**
     * 处理流程任务（审批通过或拒绝）
     */
    private void handleFlowTask(ChangeRecord changeRecord, ChangeRecordBo2 bo, ChangeItem item,
                                FlowTaskCurrentResp taskCurrent) {
        if (isApprovalAction(bo.getProcessorStatus())) {
            // 构建节点列表
//            List<FlowInstanceStartResp.FlowInstanceNode> nodeList =
//                ProcessorStatusEnum.PASS.equals(bo.getProcessorStatus()) ?
//                    new ArrayList<>() :
//                    buildNodeList(nextListResp);
            FlowTaskNextListResp nextList = smartFlowService.getNextList(
                String.valueOf(changeRecord.getProcessor().getUserId()),
                changeRecord.getTaskId(),
                JSONUtil.toBean(item.getVariables(), TreeMap.class));
            changeRecord.setNextList(JsonUtils.toJsonString(nextList));
            // 获取下一节点
            List<FlowInstanceStartResp.FlowInstanceNode> nodeList = buildNodeList(nextList);
            // 执行审批通过
            smartFlowService.approve(
                changeRecord.getTaskId(),
                item.getTitle(),
                String.valueOf(changeRecord.getProcessor().getUserId()),
                taskCurrent.getData().getNodeId(),
                taskCurrent.getData().getNodeName(),
                bo.getOpinion(),
                JSONUtil.toBean(item.getVariables(), TreeMap.class),
                nodeList
            );
        } else if (isRejectionAction(bo.getProcessorStatus())) {
            // 执行拒绝
            smartFlowService.reject(
                changeRecord.getTaskId(),
                String.valueOf(changeRecord.getProcessor().getUserId()),
                bo.getOpinion()
            );
        }
    }

    /**
     * 检查是否为审批通过相关操作
     */
    private boolean isApprovalAction(ProcessorStatusEnum status) {
        return ProcessorStatusEnum.APPROVED.equals(status) ||
            ProcessorStatusEnum.START_CHANGE.equals(status) ||
            ProcessorStatusEnum.IMPLEMENTED.equals(status) ||
            ProcessorStatusEnum.ROLLED_BACK.equals(status) ||
            ProcessorStatusEnum.PASS.equals(status);
    }

    /**
     * 检查是否为拒绝相关操作
     */
    private boolean isRejectionAction(ProcessorStatusEnum status) {
        return ProcessorStatusEnum.REJECTED.equals(status);
    }

    /**
     * 构建节点列表
     */
    private List<FlowInstanceStartResp.FlowInstanceNode> buildNodeList(FlowTaskNextListResp nextListResp) {
        List<FlowInstanceStartResp.FlowInstanceNode> nodeList = new ArrayList<>();

        for (FlowTaskNextListResp.FlowTaskNext taskNext : nextListResp.getData()) {
            FlowInstanceStartResp.FlowInstanceNode node = new FlowInstanceStartResp.FlowInstanceNode();
            node.setNodeId(taskNext.getId());
            node.setNodeName(taskNext.getName());

            // 设置处理人列表
            List<FlowInstanceStartResp.FlowInstanceHandler> handlerList = new ArrayList<>();
            if (taskNext.getCandidateList() != null) {
                for (FlowTaskNextListResp.Candidate handler : taskNext.getCandidateList()) {
                    handlerList.add(smartFlowService.createInstanceHandler(handler));
                }
            }

            node.setHandlerList(handlerList);
            nodeList.add(node);
        }

        return nodeList;
    }

    /**
     * 判断是否为验证阶段的不通过状态
     */
    private boolean isNotPassInVerificationStage(ChangeRecord changeRecord, ChangeRecordBo2 bo) {
        return ChangeStageEnum.PENDING_VERIFICATION.equals(changeRecord.getStage()) &&
            ProcessorStatusEnum.NOT_PASS.equals(bo.getProcessorStatus());
    }

    /**
     * 处理验证阶段的不通过状态
     */
    private void handleNotPassInVerificationStage(ChangeRecord changeRecord, ChangeRecordBo2 bo,
                                                  ChangeItem item, String nodeName) {
        // 更新所有待处理记录为NOT_PASS
        updateAllPendingRecords(changeRecord, ProcessorStatusEnum.NOT_PASS, "其他审批人已拒绝");

        // 更新阶段并移除处理人
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, bo.getProcessorStatus());

        item.setStage(nextStage);
        item.setProcessorList(List.of());
        if (ChangeStageEnum.COMPLETED.equals(item.getStage()) ||
            ChangeStageEnum.REJECTED.equals(item.getStage()) ||
            ChangeStageEnum.ROLLED_BACK.equals(item.getStage())) {
            item.setCompleteTime(new Date());
        }
        changeItemMapper.updateById(item);

        // 尝试回滚到实施阶段
        tryRollbackToImplementingStage(changeRecord, item, nextStage);

        // 异步发送邮件通知
        sendEmailAsync(nextStage, item.getChangeId());
    }

    /**
     * 尝试回滚到实施阶段
     */
    private void tryRollbackToImplementingStage(ChangeRecord changeRecord, ChangeItem item, ChangeStageEnum nextStage) {
        try {
            // 获取回退节点列表
            FlowTaskNextListResp backList = smartFlowService.getBackList(String.valueOf(changeRecord.getProcessor().getUserId()), changeRecord.getTaskId());

            // 查找对应IMPLEMENTING阶段的节点
            FlowTaskNextListResp.FlowTaskNext targetNode = findImplementingNode(backList);
            if (targetNode != null) {
                // 执行回退到指定节点
                smartFlowService.backToNode(
                    changeRecord.getTaskId(),
                    String.valueOf(changeRecord.getProcessor().getUserId()),
                    "验证不通过，回退到实施中阶段",
                    targetNode
                );

                // 使用回退节点信息创建下一阶段审批记录
                createNextStageRecordsFromBackNode(item, changeRecord, nextStage, targetNode);

                log.info("成功回滚流程到IMPLEMENTING阶段, changeCode: {}", item.getChangeCode());
            } else {
                throw new ServiceException("未找到回滚节点");
            }
        } catch (Exception e) {
            log.error("回滚流程失败: {}", e.getMessage(), e);
            throw new ServiceException("回滚流程失败");
        }
    }

    /**
     * 查找实施中阶段的节点
     */
    private FlowTaskNextListResp.FlowTaskNext findImplementingNode(FlowTaskNextListResp backList) {
        for (FlowTaskNextListResp.FlowTaskNext next : backList.getData()) {
            if (next.getName().contains(NODE_IMPLEMENTING)) {
                return next;
            }
        }
        return null;
    }

    /**
     * 使用回退节点信息创建下一阶段审批记录
     */
    private void createNextStageRecordsFromBackNode(ChangeItem item, ChangeRecord currentRecord, ChangeStageEnum nextStage, FlowTaskNextListResp.FlowTaskNext targetNode) {
        try {
            // 获取变更信息
            ChangeInfo info = changeInfoMapper.selectById(item.getChangeId());
            if (info == null) {
                log.error("创建下一阶段审批记录失败：未找到变更信息, changeId={}", item.getChangeId());
                return;
            }

            List<ChangeRecord> records = new ArrayList<>();
            List<String> processorIdList = new ArrayList<>();
            List<String> processorNameList = new ArrayList<>();

            // 创建下一阶段的处理记录
            for (FlowTaskNextListResp.Candidate taskNext : targetNode.getCandidateList()) {
                // 添加处理人ID和名称
                processorIdList.add(taskNext.getAssigneeId());
                processorNameList.add(taskNext.getAssigneeName());

                // 创建处理记录
                ChangeRecord record = createChangeRecordForProcessor(
                    item,
                    item.getChangeId(),
                    info.getTitle(),
                    nextStage,
                    taskNext.getAssigneeId(),
                    taskNext.getAssigneeName(),
                    info.getTeamId(),
                    info.getTeamName(),
                    info.getIsUrgentChange(),
                    info.getPriority(),
                    currentRecord.getProcessOrder() + 1,
                    false
                );

                // 填充任务信息
                populateRecordWithTaskInfo(record, item, taskNext.getAssigneeId());

                records.add(record);
            }

            // 保存新的处理记录
            if (!records.isEmpty()) {
                for (ChangeRecord record : records) {
                    changeRecordMapper.insert(record);
                    sendEmailAsync(record.getId(), record.getChangeId(), record.getProcessor().getUserId());
                }

                // 更新变更项的处理人信息
                List<SysUser> users = userMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, processorIdList));
                item.setProcessorList(users.stream().map(SysUser::getUserVo).toList());
                changeItemMapper.updateById(item);

                log.info("成功创建下一阶段审批记录, 阶段: {}, 记录数: {}", nextStage, records.size());
            }
        } catch (Exception e) {
            log.error("创建下一阶段审批记录异常", e);
        }
    }

    /**
     * 处理拒绝状态
     */
    private boolean handleRejection(ChangeRecord changeRecord, ChangeItem item, String nodeName) {
        // 更新所有待处理记录为已拒绝
        updateAllPendingRecords(changeRecord, ProcessorStatusEnum.REJECTED, "其他审批人已拒绝");

        // 更新阶段并移除处理人
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, ProcessorStatusEnum.REJECTED);

        item.setStage(nextStage);
        item.setProcessorList(List.of());

        if (ChangeStageEnum.COMPLETED.equals(nextStage) ||
            ChangeStageEnum.REJECTED.equals(nextStage) ||
            ChangeStageEnum.ROLLED_BACK.equals(nextStage)) {
            item.setCompleteTime(new Date());
        }

        changeItemMapper.updateById(item);
        // 异步发送邮件通知
        sendEmailAsync(nextStage, item.getChangeId());
        return true;
    }

    /**
     * 检查是否有待处理记录
     */
    private boolean hasPendingRecords(ChangeRecord changeRecord) {
        LambdaQueryWrapper<ChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChangeRecord::getChangeItemId, changeRecord.getChangeItemId())
            .eq(ChangeRecord::getStage, changeRecord.getStage())
            .eq(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING);

        return changeRecordMapper.selectCount(queryWrapper) > 0;
    }

    /**
     * 更新所有待处理记录
     */
    private void updateAllPendingRecords(ChangeRecord currentRecord, ProcessorStatusEnum status, String opinion) {
        LambdaQueryWrapper<ChangeRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChangeRecord::getChangeItemId, currentRecord.getChangeItemId())
            .eq(ChangeRecord::getStage, currentRecord.getStage())
            .eq(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING)
            .ne(ChangeRecord::getId, currentRecord.getId());

        List<ChangeRecord> pendingRecords = changeRecordMapper.selectList(queryWrapper);

        for (ChangeRecord record : pendingRecords) {
            record.setProcessorStatus(status);
            record.setOpinion(opinion);
            record.setProcessorTime(new Date());
            record.setIsOrSignProcessed(WhetherEnum.YES);
            changeRecordMapper.updateById(record);
        }
    }

    /**
     * 流转到下一阶段
     */
    private void transitionToNextStage(ChangeRecord changeRecord, ChangeItem item, String nodeName,
                                       ProcessorStatusEnum processorStatus, FlowTaskNextListResp nextListResp) {
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, processorStatus);

        // 更新变更项，无论阶段是否发生变化
        item.setStage(nextStage);
        item.setProcessorList(List.of());

        if (ChangeStageEnum.COMPLETED.equals(nextStage) || ChangeStageEnum.REJECTED.equals(nextStage) || ChangeStageEnum.ROLLED_BACK.equals(nextStage)) {
            item.setCompleteTime(new Date());
        }

        changeItemMapper.updateById(item);

        // 如果审批通过，需要创建下一阶段审批记录
        if (!ChangeStageEnum.REJECTED.equals(nextStage) &&
            !ChangeStageEnum.COMPLETED.equals(nextStage) &&
            !ChangeStageEnum.ROLLED_BACK.equals(nextStage)) {
            createNextStageRecords(item, changeRecord, nextStage, nextListResp);
        }
        // 异步发送邮件通知
        sendEmailAsync(nextStage, item.getChangeId());

    }

    /**
     * 处理审批通过状态
     */
    private boolean handleApproval(ChangeRecord changeRecord, ChangeItem item, String nodeName,
                                   ProcessorStatusEnum processorStatus, FlowTaskNextListResp nextListResp) {
        // 更新所有待处理记录为已通过
        updateAllPendingRecords(
            changeRecord,
            ChangeStageEnum.PENDING_VERIFICATION.equals(changeRecord.getStage()) ?
                ProcessorStatusEnum.PASS : ProcessorStatusEnum.APPROVED,
            "或签已被处理"
        );

        // 更新阶段并移除处理人
        ChangeStageEnum nextStage = determineNextStage(item, changeRecord, nodeName, processorStatus);

        item.setStage(nextStage);

        if (ChangeStageEnum.COMPLETED.equals(nextStage) ||
            ChangeStageEnum.REJECTED.equals(nextStage) ||
            ChangeStageEnum.ROLLED_BACK.equals(nextStage)) {
            item.setCompleteTime(new Date());
        }

        changeItemMapper.updateById(item);

        // 如果审批通过，需要创建下一阶段审批记录
        if (!ChangeStageEnum.REJECTED.equals(nextStage) &&
            !ChangeStageEnum.COMPLETED.equals(nextStage) &&
            !ChangeStageEnum.ROLLED_BACK.equals(nextStage)) {
            createNextStageRecords(item, changeRecord, nextStage, nextListResp);
        }
        // 异步发送邮件通知
        sendEmailAsync(nextStage, item.getChangeId());
        return true;
    }

    /**
     * 创建下一阶段审批记录
     */
    private void createNextStageRecords(ChangeItem item, ChangeRecord currentRecord, ChangeStageEnum nextStage, FlowTaskNextListResp nextListResp) {
        try {
            // 获取变更信息
            ChangeInfo info = changeInfoMapper.selectById(item.getChangeId());
            if (info == null) {
                log.error("创建下一阶段审批记录失败：未找到变更信息, changeId={}", item.getChangeId());
                return;
            }

            List<ChangeRecord> records = new ArrayList<>();
            List<String> processorIdList = new ArrayList<>();
            List<String> processorNameList = new ArrayList<>();

            // 创建下一阶段的处理记录
            for (FlowTaskNextListResp.FlowTaskNext taskNext : nextListResp.getData()) {
                if (taskNext.getCandidateList() != null) {
                    for (FlowTaskNextListResp.Candidate handler : taskNext.getCandidateList()) {
                        // 添加处理人ID和名称
                        processorIdList.add(handler.getAssigneeId());
                        processorNameList.add(handler.getAssigneeName());

                        // 创建处理记录
                        ChangeRecord record = createChangeRecordForProcessor(
                            item,
                            item.getChangeId(),
                            info.getTitle(),
                            nextStage,
                            handler.getAssigneeId(),
                            handler.getAssigneeName(),
                            info.getTeamId(),
                            info.getTeamName(),
                            info.getIsUrgentChange(),
                            info.getPriority(),
                            currentRecord.getProcessOrder() + 1,
                            false
                        );

                        record.setCountersignType(taskNext.getCountersignType());

                        // 填充任务信息
                        populateRecordWithTaskInfo(record, item, handler.getAssigneeId());

                        records.add(record);
                    }
                }
            }

            // 保存新的处理记录
            if (!records.isEmpty()) {
                for (ChangeRecord record : records) {
                    changeRecordMapper.insert(record);
                    sendEmailAsync(record.getId(), record.getChangeId(), record.getProcessor().getUserId());
                }

                // 更新变更项的处理人信息
                List<SysUser> users = userMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, processorIdList));
                item.setProcessorList(users.stream().map(SysUser::getUserVo).toList());
                changeItemMapper.updateById(item);

                log.info("成功创建下一阶段审批记录, 阶段: {}, 记录数: {}", nextStage, records.size());
            }
        } catch (Exception e) {
            log.error("创建下一阶段审批记录异常", e);
        }
    }

    @Override
    public TableDataInfo<ChangeItemVo> getChangeUnFinished(PageQuery pageQuery) {
        LoginUser user = LoginHelper.getLoginUser();

        // 构建查询条件
        LambdaQueryWrapper<ChangeItem> lqw = Wrappers.lambdaQuery();

        // 不在终态（完成、拒绝、取消、回滚）
        lqw.notIn(ChangeItem::getStage, Arrays.asList(
            ChangeStageEnum.COMPLETED,
            ChangeStageEnum.REJECTED,
            ChangeStageEnum.CANCELLED,
            ChangeStageEnum.ROLLED_BACK
        ));

        lqw.exists("SELECT 1 FROM cm_change_info WHERE cm_change_info.id = cm_change_item.change_id " +
            "AND JSON_EXTRACT(cm_change_info.requester, '$.staffId') = {0}", user.getStaffId());

        // 按照changeId倒序排序
        lqw.orderByDesc(ChangeItem::getChangeId);

        // 执行分页查询
        Page<ChangeItemVo> result = changeItemMapper.selectVoPage(pageQuery.build(), lqw);

        return TableDataInfo.build(result);
    }

    /**
     * 异步发送变更状态邮件
     *
     * @param stage    变更阶段
     * @param changeId 变更ID
     */
    private void sendEmailAsync(ChangeStageEnum stage, Long changeId) {
        CompletableFuture.runAsync(() -> {
            sendEmailWithRetry(stage, changeId);
        });
    }

    /**
     * 发送变更状态邮件，支持重试
     *
     * @param stage    变更阶段
     * @param changeId 变更ID
     */
    private void sendEmailWithRetry(ChangeStageEnum stage, Long changeId) {
        for (int attempt = 1; attempt <= EMAIL_MAX_RETRIES; attempt++) {
            try {
                // 检查变更信息是否存在
                ChangeInfo info = changeInfoMapper.selectById(changeId);
                if (info == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("变更信息不存在，进行第{}次重试, stage: {}, changeId: {}", attempt, stage, changeId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后变更信息仍不存在, stage: {}, changeId: {}", EMAIL_MAX_RETRIES, stage, changeId);
                    return;
                }

                // 检查变更项是否存在
                ChangeItem item = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>()
                    .eq(ChangeItem::getChangeId, changeId));
                if (item == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("变更项信息不存在，进行第{}次重试, stage: {}, changeId: {}", attempt, stage, changeId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后变更项信息仍不存在, stage: {}, changeId: {}", EMAIL_MAX_RETRIES, stage, changeId);
                    return;
                }

                // 发送邮件
                emailService.sendEmailByChangeInfoId(stage, changeId);
                log.info("异步发送变更状态邮件成功, stage: {}, changeId: {}", stage, changeId);
                return; // 发送成功，退出循环
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("邮件发送重试中断, stage: {}, changeId: {}", stage, changeId, e);
                return;
            } catch (Exception e) {
                log.error("第{}次发送邮件失败: {}, stage: {}, changeId: {}", attempt, e.getMessage(), stage, changeId);
                if (attempt < EMAIL_MAX_RETRIES) {
                    try {
                        Thread.sleep(EMAIL_RETRY_DELAY_MS * attempt); // 逐次增加重试等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    log.error("邮件发送失败达到最大重试次数: {}", EMAIL_MAX_RETRIES);
                }
            }
        }
    }

    /**
     * 发送待审批邮件，支持重试
     *
     * @param recordId 记录ID
     * @param changeId 变更ID
     * @param userId   用户ID
     */
    private void sendEmailWithRetry(Long recordId, Long changeId, Long userId) {
        for (int attempt = 1; attempt <= EMAIL_MAX_RETRIES; attempt++) {
            try {
                // 检查变更信息是否存在
                ChangeInfo info = changeInfoMapper.selectById(changeId);
                if (info == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("变更信息不存在，进行第{}次重试, changeId: {}, userId: {}", attempt, changeId, userId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后变更信息仍不存在, changeId: {}, userId: {}", EMAIL_MAX_RETRIES, changeId, userId);
                    return;
                }

                // 检查变更项是否存在
                ChangeItem item = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>()
                    .eq(ChangeItem::getChangeId, changeId));
                if (item == null) {
                    if (attempt < EMAIL_MAX_RETRIES) {
                        log.warn("变更项信息不存在，进行第{}次重试, changeId: {}, userId: {}", attempt, changeId, userId);
                        Thread.sleep(EMAIL_RETRY_DELAY_MS);
                        continue;
                    }
                    log.error("重试{}次后变更项信息仍不存在, changeId: {}, userId: {}", EMAIL_MAX_RETRIES, changeId, userId);
                    return;
                }

                // 发送邮件
                emailService.sendApprovalChangeEmail(recordId, changeId, userId);
                log.info("异步发送变更状态邮件成功, changeId: {}, userId: {}", changeId, userId);
                return; // 发送成功，退出循环
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("邮件发送重试中断, changeId: {}, userId: {}", changeId, userId, e);
                return;
            } catch (Exception e) {
                log.error("第{}次发送邮件失败: {}, changeId: {}, userId: {}", attempt, e.getMessage(), changeId, userId);
                if (attempt < EMAIL_MAX_RETRIES) {
                    try {
                        Thread.sleep(EMAIL_RETRY_DELAY_MS * attempt); // 逐次增加重试等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                } else {
                    log.error("邮件发送失败达到最大重试次数: {}", EMAIL_MAX_RETRIES);
                }
            }
        }
    }

    /**
     * 确定下一个阶段状态
     *
     * @param item            变更项
     * @param changeRecord    变更记录
     * @param nodeName        节点名称
     * @param processorStatus 处理状态
     * @return 下一阶段
     */
    private ChangeStageEnum determineNextStage(ChangeItem item, ChangeRecord changeRecord, String nodeName, ProcessorStatusEnum processorStatus) {
        if (item.getTeamId().equals(1920403189165473793L)) {
            return dsiStageTransition(changeRecord, nodeName, processorStatus);
        } else if (item.getTeamId().equals(1920403156194050049L)) {
            return appStageTransition(changeRecord, nodeName, processorStatus, item.getIsUrgentChange());
        } else if (item.getTeamId().equals(1920403120731209729L)) {
            return infraStageTransition(changeRecord, nodeName, processorStatus, item.getIsUrgentChange());
        }
        throw new ServiceException("未找到匹配的团队ID，无法确定下一阶段");
    }

    /**
     * 异步发送待审批邮件
     *
     * @param recordId 记录ID
     * @param changeId 变更ID
     * @param userId   用户ID
     */
    private void sendEmailAsync(Long recordId, Long changeId, Long userId) {
        CompletableFuture.runAsync(() -> sendEmailWithRetry(recordId, changeId, userId));
    }
}

