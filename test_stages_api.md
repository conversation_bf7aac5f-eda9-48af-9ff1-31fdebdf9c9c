# 变更统计接口阶段筛选功能测试

## 修改内容总结

### 1. Controller 层修改
- `ChangeInfoController.statistics()` 方法添加了 `List<Integer> stages` 参数
- `ChangeInfoController.exportStatistics()` 方法添加了 `List<Integer> stages` 参数
- 参数设置为非必填：`@RequestParam(required = false)`

### 2. Service 接口修改
- `IChangeItemService.getStatistics()` 方法签名添加了 `List<Integer> stages` 参数

### 3. Service 实现修改
- `ChangeItemServiceImpl.getStatistics()` 方法添加了 stages 参数处理
- `buildStatisticsQueryWrapper()` 方法添加了阶段筛选逻辑：
  - 如果 stages 不为空，使用 `in` 条件筛选指定阶段
  - 如果 stages 为空，保持原有逻辑（排除草稿状态）

## API 使用示例

### 1. 不指定阶段（保持原有行为）
```
GET /business/change/statistics?dateStart=202501&dateEnd=202501&teamId=1
```

### 2. 筛选单个阶段
```
GET /business/change/statistics?dateStart=202501&dateEnd=202501&stages=2
```

### 3. 筛选多个阶段
```
GET /business/change/statistics?dateStart=202501&dateEnd=202501&stages=2,3,4
```

### 4. 导出功能同样支持阶段筛选
```
GET /business/change/statistics/export?dateStart=202501&dateEnd=202501&stages=2,3,4
```

## 阶段枚举值参考

| 代码 | 阶段名称 |
|------|----------|
| 1    | 草稿 |
| 2    | 计划中 |
| 3    | 待审批 |
| 4    | 已审批 |
| 5    | 实施中 |
| 6    | 已完成 |
| 7    | 已拒绝 |
| 8    | 已回滚 |
| 9    | 已取消 |
| 10   | 待验证 |
| 11   | 已延期 |
| 12   | 超过7天未关闭 |

## 兼容性说明

- 新增的 `stages` 参数为可选参数，不会影响现有的API调用
- 当不传递 `stages` 参数时，保持原有的查询逻辑（排除草稿状态）
- 当传递 `stages` 参数时，按用户指定的阶段进行筛选
